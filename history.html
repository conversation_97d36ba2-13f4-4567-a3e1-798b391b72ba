<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开奖历史记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            color: #333;
        }
        .back-btn {
            padding: 8px 15px;
            background: #4dd07d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .top-bar {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            gap: 10px;
            flex: 1;
        }
        .tab {
            padding: 10px 20px;
            border: 1px solid #4dd07d;
            border-radius: 4px;
            cursor: pointer;
            color: #333;
            background: white;
        }
        .tab.active {
            background: #4dd07d;
            color: white;
        }
        .year-select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 20px;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .history-table th {
            background: #f8f8f8;
            padding: 12px;
            text-align: center;
            font-weight: normal;
            color: #666;
        }
        .history-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        .numbers-box {
            display: inline-block;
            border: 1px solid #4dd07d;
            border-radius: 4px;
            padding: 10px 15px;
            background: rgba(77, 208, 125, 0.05);
            margin-top: 10px;
            white-space: nowrap;
            min-width: 100%;
            text-align: center;
        }
        .draw-info {
            color: #666;
            font-size: 14px;
            padding: 0 10px;
            white-space: normal;
        }
        .draw-period {
            color: #4dd07d;
            font-weight: bold;
            margin-right: 15px;
        }
        .number {
            width: 36px;
            height: 36px;
            background-size: cover;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 700;
            font-size: 16px;
            text-shadow: 0 0 2px rgba(255, 255, 255, 0.9);
        }
        .number-info {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .red { background-image: url('images/bg-amage-1.png'); }
        .blue { background-image: url('images/bg-amage-2.png'); }
        .green { background-image: url('images/bg-amage-3.png'); }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            margin-top: 20px;
            padding: 0 10px;
        }
        .page-btn {
            padding: 8px 10px;
            border: 1px solid #4dd07d;
            background: white;
            color: #4dd07d;
            border-radius: 4px;
            cursor: pointer;
            white-space: nowrap;
            min-width: auto;
            font-size: 14px;
        }
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .page-btn.active {
            background: #4dd07d;
            color: white;
        }
        .number-container {
            display: inline-block;
            text-align: center;
            margin: 0 3px;
            vertical-align: top;
        }
        .plus-sign {
            color: #4dd07d;
            font-size: 24px;
            margin: 0 5px;
            display: inline-block;
            vertical-align: top;
            line-height: 36px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">开奖历史记录</h1>
            <a href="javascript:history.back()" class="back-btn">返回</a>
        </div>
        <div class="top-bar">
            <div class="tabs">
                <button type="button" class="tab active" data-index="0">Two-Color</button>
                <button type="button" class="tab" data-index="1">澳门</button>
                <button type="button" class="tab" data-index="2">香港</button>
            </div>
            <select id="yearSelect" class="year-select">
                <option value="2025">2025年</option>
                <option value="2024">2024年</option>
                <option value="2023">2023年</option>
            </select>
        </div>
        <table class="history-table">
            <thead>
                <tr>
                    <th>开奖信息</th>
                </tr>
            </thead>
            <tbody id="historyTableBody"></tbody>
        </table>
        <div class="pagination" id="pagination"></div>
    </div>

    <script src="js/api.js"></script>
    <script>
        class HistoryPage {
            constructor() {
                // 从URL参数获取彩票类型，默认为0（Two-Color）
                const urlParams = new URLSearchParams(window.location.search);
                this.currentTabIndex = parseInt(urlParams.get('type')) || 0;
                this.currentPage = 1;
                this.currentYear = new Date().getFullYear();
                this.pageSize = 10;
                this.tabs = document.querySelectorAll('.tab');
                this.yearSelect = document.getElementById('yearSelect');
                this.tableBody = document.getElementById('historyTableBody');
                this.pagination = document.getElementById('pagination');

                this.initializeEventListeners();
                this.updateTabDisplay();
                this.updatePageTitle();
                this.loadHistory();
            }

            initializeEventListeners() {
                // 年份选择事件
                this.yearSelect.addEventListener('change', async () => {
                    this.currentYear = parseInt(this.yearSelect.value);
                    this.currentPage = 1;
                    await this.loadHistory();
                });

                // 标签页切换事件
                this.tabs.forEach((tab, index) => {
                    tab.addEventListener('click', () => {
                        this.switchTab(index);
                    });
                });
            }

            switchTab(index) {
                this.currentTabIndex = index;
                this.currentPage = 1;
                this.updateTabDisplay();
                this.updatePageTitle();
                this.loadHistory();
            }

            updateTabDisplay() {
                this.tabs.forEach((tab, index) => {
                    if (index === this.currentTabIndex) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                });
            }

            updatePageTitle() {
                const titles = ['Two-Color开奖历史记录', '澳门开奖历史记录', '香港开奖历史记录'];
                document.title = titles[this.currentTabIndex] || '开奖历史记录';
                document.querySelector('.title').textContent = titles[this.currentTabIndex] || '开奖历史记录';
            }

            async loadHistory() {
                try {
                    console.log('Loading history for tab:', this.currentTabIndex, 'page:', this.currentPage, 'year:', this.currentYear);
                    this.tableBody.innerHTML = '<tr><td><div class="draw-info">加载中...</div></td></tr>';
                    const history = await api.getHistory(this.currentTabIndex, this.currentPage, this.currentYear);
                    console.log('History data:', history);
                    this.renderHistory(history);
                } catch (error) {
                    console.error('加载历史记录失败:', error);
                    this.tableBody.innerHTML = '<tr><td><div class="draw-info">加载失败</div></td></tr>';
                    this.pagination.innerHTML = '';
                }
            }

            renderHistory(history) {
                let html = '';
                if (!history.records || history.records.length === 0) {
                    html = '<tr><td><div class="draw-info">暂无数据</div></td></tr>';
                    this.tableBody.innerHTML = html;
                    this.pagination.innerHTML = '';
                    return;
                }

                history.records.forEach(record => {
                    const numbersHtml = record.numbers.map((num, index) => {
                        if (index === 6) {
                            return `<span class="plus-sign">+</span><div class="number-container">
                                <div class="number ${num.color}">${num.value}</div>
                                <div class="number-info">${num.zodiac || ''}</div>
                            </div>`;
                        }
                        return `<div class="number-container">
                            <div class="number ${num.color}">${num.value}</div>
                            <div class="number-info">${num.zodiac || ''}</div>
                        </div>`;
                    }).join('');

                    html += `
                        <tr>
                            <td>
                                <div class="draw-info">
                                    <span class="draw-period">第${record.period}期</span>
                                    <span>${record.drawTime}</span>
                                </div>
                                <div class="numbers-box">${numbersHtml}</div>
                            </td>
                        </tr>
                    `;
                });
                this.tableBody.innerHTML = html;

                // 使用API返回的页码信息
                this.currentPage = history.page || this.currentPage;
                const totalPages = Math.ceil(history.total / this.pageSize);
                if (totalPages > 0) {
                    this.renderPagination(totalPages);
                } else {
                    this.pagination.innerHTML = '';
                }
            }

            renderPagination(totalPages) {
                let html = '';

                // 上一页按钮
                html += `
                    <button class="page-btn" 
                        ${this.currentPage === 1 ? 'disabled' : ''}
                        onclick="historyPage.changePage(${this.currentPage - 1})">
                        上一页
                    </button>
                `;

                // 页码按钮
                for (let i = 1; i <= totalPages; i++) {
                    if (
                        i === 1 || 
                        i === totalPages || 
                        (i >= this.currentPage - 2 && i <= this.currentPage + 2)
                    ) {
                        html += `
                            <button class="page-btn ${i === this.currentPage ? 'active' : ''}"
                                onclick="historyPage.changePage(${i})">
                                ${i}
                            </button>
                        `;
                    } else if (
                        i === this.currentPage - 3 || 
                        i === this.currentPage + 3
                    ) {
                        html += '<span>...</span>';
                    }
                }

                // 下一页按钮
                html += `
                    <button class="page-btn" 
                        ${this.currentPage === totalPages ? 'disabled' : ''}
                        onclick="historyPage.changePage(${this.currentPage + 1})">
                        下一页
                    </button>
                `;

                this.pagination.innerHTML = html;
            }

            async changePage(page) {
                this.currentPage = page;
                await this.loadHistory();
                window.scrollTo(0, 0);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.historyPage = new HistoryPage();
        });
    </script>
</body>
</html> 