// API接口配置
const API_BASE_URL = 'https://api.example.com';

const API_ENDPOINTS = {
    NEW_HK: 'https://www.49tukua.xyz/api/lottery/latest',
    NEW_HK_HISTORY: 'https://www.49tukua.xyz/api/lottery/history',
    MACAU: 'https://macaumarksix.com/api/macaujc2.com',
    HK: 'https://www.macaumarksix.com/api/hkjc.com',
    MACAU_HISTORY: 'https://api.macaumarksix.com/history/macaujc2/y/',
    HK_HISTORY: 'https://api.macaumarksix.com/history/hkjc/y/'
};

const api = {
    // 转换API响应数据为统一格式
    transformNewHKData(data) {
        const { data: result } = data;
        return {
            currentPeriod: result.draw_no,
            nextPeriod: result.expect_next.toString(),
            nextDrawTime: result.next_time,
            numbers: result.balls.map(ball => {
                const zodiacText = this.convertZodiacToSimplified(ball.shengXiao || '');
                const elementText = ball.wuXing || '';
                const zodiacWithElement = zodiacText && elementText ? `${zodiacText}/${elementText}` : zodiacText;

                return {
                    value: ball.number,
                    color: this.convertColor(ball.color),
                    zodiac: zodiacWithElement,
                    element: elementText
                };
            })
        };
    },

    transformNewHKHistoryData(data) {
        const { data: result } = data;
        return {
            total: result.total,
            page: result.page,
            records: result.list.map(item => ({
                period: item.draw_no,
                drawTime: item.draw_time,
                numbers: item.balls.map(ball => {
                    const zodiacText = this.convertZodiacToSimplified(ball.shengXiao || '');
                    const elementText = ball.wuXing || '';
                    const zodiacWithElement = zodiacText && elementText ? `${zodiacText}/${elementText}` : zodiacText;

                    return {
                        value: ball.number,
                        color: this.convertColor(ball.color),
                        zodiac: zodiacWithElement,
                        element: elementText
                    };
                })
            }))
        };
    },

    transformMacauHKData(data) {
        const { data: result } = data;
        return {
            currentPeriod: result.period,
            nextPeriod: result.nextIntLotteryNumber.toString(),
            nextDrawTime: result.nextLotteryTime,
            numbers: result.numberList.map(num => ({
                value: num.number,
                color: this.convertColor(num.color),
                zodiac: num.shengXiao,
                element: num.wuXing
            }))
        };
    },

    // 转换新香港接口数据
    transformNewHKAPIData(data) {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('无效的数据格式');
        }

        const result = data[0];
        const numbers = result.openCode.split(',');
        const colors = result.wave.split(',');
        const zodiacs = result.zodiac.split(',');

        return {
            currentPeriod: result.expect,
            nextPeriod: '', // 新接口没有下期期号
            nextDrawTime: '', // 新接口没有下期开奖时间
            drawTime: result.openTime,
            numbers: numbers.map((num, index) => {
                const numberValue = num.trim();
                const zodiacText = zodiacs[index] ? this.convertZodiacToSimplified(zodiacs[index].trim()) : '';
                const elementText = this.getElementByNumber(numberValue);
                const zodiacWithElement = zodiacText && elementText ? `${zodiacText}/${elementText}` : zodiacText;

                return {
                    value: numberValue,
                    color: colors[index] ? colors[index].trim() : 'red',
                    zodiac: zodiacWithElement,
                    element: elementText
                };
            })
        };
    },

    // 转换新澳门接口数据
    transformNewMacauAPIData(data) {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('无效的数据格式');
        }

        const result = data[0];
        const numbers = result.openCode.split(',');
        const colors = result.wave.split(',');
        const zodiacs = result.zodiac.split(',');

        return {
            currentPeriod: result.expect,
            nextPeriod: '', // 新接口没有下期期号
            nextDrawTime: '', // 新接口没有下期开奖时间
            drawTime: result.openTime,
            numbers: numbers.map((num, index) => {
                const numberValue = num.trim();
                const zodiacText = zodiacs[index] ? this.convertZodiacToSimplified(zodiacs[index].trim()) : '';
                const elementText = this.getElementByNumber(numberValue);
                const zodiacWithElement = zodiacText && elementText ? `${zodiacText}/${elementText}` : zodiacText;

                return {
                    value: numberValue,
                    color: colors[index] ? colors[index].trim() : 'red',
                    zodiac: zodiacWithElement,
                    element: elementText
                };
            })
        };
    },

    convertColor(colorCode) {
        switch (colorCode) {
            case 1: return 'red';
            case 2: return 'blue';
            case 3: return 'green';
            default: return 'red';
        }
    },

    // 生肖繁体转简体
    convertZodiacToSimplified(zodiac) {
        const zodiacMap = {
            '鼠': '鼠',
            '牛': '牛',
            '虎': '虎',
            '兔': '兔',
            '龍': '龙',
            '蛇': '蛇',
            '馬': '马',
            '羊': '羊',
            '猴': '猴',
            '雞': '鸡',
            '狗': '狗',
            '豬': '猪'
        };
        return zodiacMap[zodiac] || zodiac;
    },

    // 根据号码获取五行
    getElementByNumber(number) {
        const num = parseInt(number);

        // 金: 03,04,11,12,25,26,33,34,41,42
        if ([3,4,11,12,25,26,33,34,41,42].includes(num)) {
            return '金';
        }
        // 木: 07,08,15,16,23,24,37,38,45,46
        else if ([7,8,15,16,23,24,37,38,45,46].includes(num)) {
            return '木';
        }
        // 水: 13,14,21,22,29,30,43,44
        else if ([13,14,21,22,29,30,43,44].includes(num)) {
            return '水';
        }
        // 火: 01,02,09,10,17,18,31,32,39,40,47,48
        else if ([1,2,9,10,17,18,31,32,39,40,47,48].includes(num)) {
            return '火';
        }
        // 土: 05,06,19,20,27,28,35,36,49
        else if ([5,6,19,20,27,28,35,36,49].includes(num)) {
            return '土';
        }
        else {
            return '';
        }
    },

    // 获取彩票结果
    async getLotteryResults(tabIndex) {
        try {
            let response;
            switch (tabIndex) {
                case 0: // 新香港
                    response = await fetch(API_ENDPOINTS.NEW_HK);
                    const newHKData = await response.json();
                    return this.transformNewHKData(newHKData);

                case 1: // 澳门
                    response = await fetch(API_ENDPOINTS.MACAU);
                    const macauData = await response.json();
                    return this.transformNewMacauAPIData(macauData);

                case 2: // 香港
                    response = await fetch(API_ENDPOINTS.HK);
                    const hkData = await response.json();
                    return this.transformNewHKAPIData(hkData);

                default:
                    throw new Error('未知的彩票类型');
            }
        } catch (error) {
            console.error('获取彩票数据失败:', error);
            throw error;
        }
    },

    // 获取倒计时
    async getCountdown(tabIndex) {
        try {
            const lotteryData = await this.getLotteryResults(tabIndex);

            // 如果没有下期开奖时间信息，返回默认值
            if (!lotteryData.nextDrawTime) {
                return '00:00:00';
            }

            const nextDrawTime = new Date(lotteryData.nextDrawTime);
            const now = new Date();

            // 计算时间差
            let diff = nextDrawTime - now;
            if (diff < 0) {
                return '00:00:00';
            }

            // 转换为时分秒
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);

            // 格式化输出
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        } catch (error) {
            console.error('获取倒计时失败:', error);
            return '00:00:00';
        }
    },

    // 转换历史记录数据
    transformHistoryData(data, page) {
        if (!data || !data.result || !Array.isArray(data.data)) {
            return {
                total: 0,
                page: page,
                records: []
            };
        }

        // 计算开始和结束索引，实现前端分页
        const startIndex = (page - 1) * 10;
        const endIndex = startIndex + 10;
        const pageData = data.data.slice(startIndex, endIndex);

        const colorMap = {
            'red': 'red',
            'blue': 'blue',
            'green': 'green'
        };

        return {
            total: data.data.length,
            page: page,
            records: pageData.map(item => ({
                period: item.expect,
                drawTime: item.openTime,
                numbers: item.openCode.split(',').map((num, index) => {
                    const color = item.wave.split(',')[index].trim().toLowerCase();
                    const numberValue = num.trim();
                    const zodiacText = item.zodiac.split(',')[index] ? this.convertZodiacToSimplified(item.zodiac.split(',')[index].trim()) : '';
                    const elementText = this.getElementByNumber(numberValue);
                    const zodiacWithElement = zodiacText && elementText ? `${zodiacText}/${elementText}` : zodiacText;

                    return {
                        value: numberValue,
                        color: colorMap[color] || 'red', // 如果找不到对应的颜色，默认使用红色
                        zodiac: zodiacWithElement,
                        element: elementText
                    };
                })
            }))
        };
    },

    // 获取历史记录
    async getHistory(tabIndex, page = 1, year = new Date().getFullYear()) {
        try {
            let url;
            if (tabIndex === 0) { // 新香港
                url = `${API_ENDPOINTS.NEW_HK_HISTORY}?page=${page}&year=${year}&draw_no=`;
                const response = await fetch(url);
                const data = await response.json();
                return this.transformNewHKHistoryData(data);
            } else if (tabIndex === 1) { // 澳门
                url = `${API_ENDPOINTS.MACAU_HISTORY}${year}`;
                const response = await fetch(url);
                const data = await response.json();
                if (!data.result) {
                    throw new Error(data.message || '获取数据失败');
                }
                return this.transformHistoryData(data, page);
            } else { // 香港
                url = `${API_ENDPOINTS.HK_HISTORY}${year}`;
                const response = await fetch(url);
                const data = await response.json();
                if (!data.result) {
                    throw new Error(data.message || '获取数据失败');
                }
                return this.transformHistoryData(data, page);
            }
        } catch (error) {
            console.error('获取历史记录失败:', error);
            return {
                total: 0,
                records: []
            };
        }
    }
}; 