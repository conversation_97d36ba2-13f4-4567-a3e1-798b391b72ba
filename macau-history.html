<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澳门开奖历史记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            color: #333;
        }
        .back-btn {
            padding: 8px 15px;
            background: #4dd07d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .back-btn:hover {
            background: #45c072;
        }
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .tabs {
            display: flex;
            gap: 10px;
        }
        .tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        .tab.active {
            background: #4dd07d;
            color: white;
            border-color: #4dd07d;
        }
        .year-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .history-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #eee;
        }
        .history-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .history-table tr:last-child td {
            border-bottom: none;
        }
        .draw-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        .draw-period {
            font-weight: 600;
            color: #333;
        }
        .numbers-box {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        .number-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }
        .number {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .number.red {
            background: #ff4757;
        }
        .number.blue {
            background: #3742fa;
        }
        .number.green {
            background: #2ed573;
        }
        .number-info {
            font-size: 10px;
            color: #666;
            text-align: center;
            min-height: 12px;
            line-height: 12px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px 0;
        }
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        .page-btn:hover:not(:disabled) {
            background: #f8f9fa;
        }
        .page-btn.active {
            background: #4dd07d;
            color: white;
            border-color: #4dd07d;
        }
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .plus-sign {
            color: #4dd07d;
            font-size: 24px;
            margin: 0 5px;
            display: inline-block;
            vertical-align: top;
            line-height: 36px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">澳门开奖历史记录</h1>
            <a href="index.html" class="back-btn">返回</a>
        </div>
        <div class="top-bar">
            <div class="tabs">
                <button type="button" class="tab active" data-index="1">澳门</button>
            </div>
            <select id="yearSelect" class="year-select">
                <option value="2025">2025年</option>
                <option value="2024">2024年</option>
                <option value="2023">2023年</option>
            </select>
        </div>
        <table class="history-table">
            <thead>
                <tr>
                    <th>开奖信息</th>
                </tr>
            </thead>
            <tbody id="historyTableBody"></tbody>
        </table>
        <div class="pagination" id="pagination"></div>
    </div>

    <script src="js/api.js"></script>
    <script>
        class MacauHistoryPage {
            constructor() {
                this.currentTabIndex = 1; // 澳门的索引
                this.currentPage = 1;
                this.currentYear = new Date().getFullYear();
                this.pageSize = 10;
                this.yearSelect = document.getElementById('yearSelect');
                this.tableBody = document.getElementById('historyTableBody');
                this.pagination = document.getElementById('pagination');

                this.initializeEventListeners();
                this.loadHistory();
            }

            initializeEventListeners() {
                this.yearSelect.addEventListener('change', async () => {
                    this.currentYear = parseInt(this.yearSelect.value);
                    this.currentPage = 1;
                    await this.loadHistory();
                });
            }

            async loadHistory() {
                try {
                    console.log('Loading Macau history for page:', this.currentPage, 'year:', this.currentYear);
                    this.tableBody.innerHTML = '<tr><td><div class="draw-info">加载中...</div></td></tr>';
                    const history = await api.getHistory(this.currentTabIndex, this.currentPage, this.currentYear);
                    console.log('Macau history data:', history);
                    this.renderHistory(history);
                } catch (error) {
                    console.error('加载澳门历史记录失败:', error);
                    this.tableBody.innerHTML = '<tr><td><div class="draw-info">加载失败</div></td></tr>';
                    this.pagination.innerHTML = '';
                }
            }

            renderHistory(history) {
                let html = '';
                if (!history.records || history.records.length === 0) {
                    html = '<tr><td><div class="draw-info">暂无数据</div></td></tr>';
                    this.tableBody.innerHTML = html;
                    this.pagination.innerHTML = '';
                    return;
                }

                history.records.forEach(record => {
                    const numbersHtml = record.numbers.map((num, index) => {
                        if (index === 6) {
                            return `<span class="plus-sign">+</span><div class="number-container">
                                <div class="number ${num.color}">${num.value}</div>
                                <div class="number-info">${num.zodiac || ''}</div>
                            </div>`;
                        }
                        return `<div class="number-container">
                            <div class="number ${num.color}">${num.value}</div>
                            <div class="number-info">${num.zodiac || ''}</div>
                        </div>`;
                    }).join('');

                    html += `
                        <tr>
                            <td>
                                <div class="draw-info">
                                    <span class="draw-period">第${record.period}期</span>
                                    <span>${record.drawTime}</span>
                                </div>
                                <div class="numbers-box">${numbersHtml}</div>
                            </td>
                        </tr>
                    `;
                });

                this.tableBody.innerHTML = html;
                
                // 渲染分页
                const totalPages = Math.ceil(history.total / this.pageSize);
                this.renderPagination(totalPages);
            }

            renderPagination(totalPages) {
                let html = '';

                // 上一页按钮
                html += `
                    <button class="page-btn" 
                        ${this.currentPage === 1 ? 'disabled' : ''}
                        onclick="macauHistoryPage.changePage(${this.currentPage - 1})">
                        上一页
                    </button>
                `;

                // 页码按钮
                for (let i = 1; i <= totalPages; i++) {
                    if (
                        i === 1 || 
                        i === totalPages || 
                        (i >= this.currentPage - 2 && i <= this.currentPage + 2)
                    ) {
                        html += `
                            <button class="page-btn ${i === this.currentPage ? 'active' : ''}"
                                onclick="macauHistoryPage.changePage(${i})">
                                ${i}
                            </button>
                        `;
                    } else if (
                        i === this.currentPage - 3 || 
                        i === this.currentPage + 3
                    ) {
                        html += '<span>...</span>';
                    }
                }

                // 下一页按钮
                html += `
                    <button class="page-btn" 
                        ${this.currentPage === totalPages ? 'disabled' : ''}
                        onclick="macauHistoryPage.changePage(${this.currentPage + 1})">
                        下一页
                    </button>
                `;

                this.pagination.innerHTML = html;
            }

            async changePage(page) {
                this.currentPage = page;
                await this.loadHistory();
                window.scrollTo(0, 0);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.macauHistoryPage = new MacauHistoryPage();
        });
    </script>
</body>
</html>
