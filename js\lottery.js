class LotteryManager {
    constructor() {
        this.currentTabIndex = 0;
        this.tabs = document.querySelectorAll('.tab');
        this.countdownTimer = document.getElementById('countdown-timer');
        this.currentPeriod = document.getElementById('current-period');
        this.nextPeriod = document.getElementById('next-period');
        this.nextDrawTime = document.getElementById('next-draw-time');
        this.lotteryNumbers = document.querySelector('.lottery-numbers');
        this.refreshInterval = null;
        
        // 调试输出
        console.log('历史记录链接元素:', this.historyLink);
        
        this.initializeEventListeners();
        this.startCountdown();
        this.updateDisplay();
        this.startAutoRefresh();
    }

    initializeEventListeners() {
        // 为每个标签添加点击事件
        this.tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => this.switchTab(index));
        });

        // 使用事件委托处理历史记录链接点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('.history')) {
                console.log('历史记录被点击');
                e.preventDefault();
                window.location.href = 'history.html';
            }
        });
    }

    async switchTab(index) {
        this.currentTabIndex = index;
        
        // 更新标签样式
        this.tabs.forEach((tab, i) => {
            if (i === index) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // 获取并显示对应彩票数据
        await this.updateDisplay();
        
        // 切换标签时检查是否需要启动或停止自动刷新
        this.startAutoRefresh();
    }

    // 检查是否在新香港开奖时间段
    isNewHKDrawTime() {
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const currentTime = hours * 60 + minutes; // 转换为分钟计数
        const startTime = 13 * 60 + 58; // 13:58
        const endTime = 14 * 60 + 8;    // 14:08

        return currentTime >= startTime && currentTime <= endTime;
    }

    // 启动自动刷新
    startAutoRefresh() {
        // 清除现有的刷新定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }

        // 只有在新香港标签时才设置自动刷新
        if (this.currentTabIndex === 0) {
            // 首次检查是否在开奖时间段
            if (this.isNewHKDrawTime()) {
                this.updateDisplay();
            }

            // 设置定时器，每秒检查一次
            this.refreshInterval = setInterval(() => {
                if (this.isNewHKDrawTime()) {
                    this.updateDisplay();
                }
            }, 1000);
        }
    }

    async updateDisplay() {
        try {
            // 获取当前彩票数据
            const lotteryData = await api.getLotteryResults(this.currentTabIndex);
            
            // 更新期号
            this.currentPeriod.textContent = lotteryData.currentPeriod;
            this.nextPeriod.textContent = lotteryData.nextPeriod;
            
            // 更新开奖号码
            this.updateLotteryNumbers(lotteryData.numbers);
            
            // 更新下期开奖时间
            this.updateNextDrawTime(lotteryData.nextDrawTime);

            // 更新标签中的开奖时间
            const drawDate = new Date(lotteryData.nextDrawTime);
            const month = drawDate.getMonth() + 1;
            const day = drawDate.getDate();
            this.tabs[this.currentTabIndex].querySelector('.draw-time').textContent = 
                `${month}月${day}日开奖`;
        } catch (error) {
            console.error('更新彩票数据失败:', error);
        }
    }

    updateLotteryNumbers(numbers) {
        const containers = this.lotteryNumbers.querySelectorAll('.number-container');
        numbers.forEach((number, index) => {
            if (containers[index]) {
                const numberElement = containers[index].querySelector('.number');
                const infoElement = containers[index].querySelector('.number-info');
                
                numberElement.textContent = number.value;
                numberElement.className = `number ${number.color}`;
                infoElement.textContent = `${number.zodiac}/${number.element}`;
            }
        });
    }

    updateNextDrawTime(nextDrawTime) {
        const date = new Date(nextDrawTime);
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const weekday = weekdays[date.getDay()];
        
        this.nextDrawTime.textContent = 
            `${year}-${month}-${day} ${hours}:${minutes} 星期${weekday}`;
    }

    startCountdown() {
        const updateCountdown = async () => {
            try {
                const countdown = await api.getCountdown(this.currentTabIndex);
                this.countdownTimer.textContent = countdown;
            } catch (error) {
                console.error('更新倒计时失败:', error);
            }
        };

        // 立即更新一次
        updateCountdown();
        
        // 每秒更新一次
        setInterval(updateCountdown, 1000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.lotteryManager = new LotteryManager();
}); 