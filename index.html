<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开奖信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            /*padding: 20px;*/
        }
        .lottery-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 1px;
        }
        .tab {
            padding: 8px 15px;
            cursor: pointer;
            border: none;
            text-align: center;
            color: #333;
            background: white;
            border-radius: 8px 8px 0 0;
            width: calc(33.33% - 7px);
            border: 1px solid #4dd07d;
            border-bottom: none;
            position: relative;
        }
        .tab .draw-time {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .tab.active .draw-time {
            color: rgba(255, 255, 255, 0.9);
        }
        .tab.active {
            color: white;
            background: #4dd07d;
            border-color: #4dd07d;
        }
        .lottery-box {
            border: 1px solid #4dd07d;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
            position: relative;
        }
        .lottery-content {
            padding: 15px;
            padding-top: 35px;
            padding-bottom: 0;
        }
        .period {
            position: absolute;
            top: 8px;
            left: 10px;
            font-size: 14px;
            color: #333;
        }
        .period span {
            color: #4dd07d;
        }
        .countdown {
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            color: #666;
            font-size: 14px;
        }
        .number-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }
        .number {
            width: 40px;
            height: 40px;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: 700;
            font-size: 18px;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            text-shadow: 0 0 2px rgba(255, 255, 255, 0.9);
        }
        .number-info {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .lottery-numbers {
            display: flex;
            justify-content: center;
            gap: 6px;
            margin: 5px 0 10px;
            align-items: flex-start;
        }
        .plus-sign {
            color: #4dd07d;
            font-size: 24px;
            margin: 0 2px;
        }
        .red {
            background-image: url('images/bg-amage-1.png');
        }
        .blue {
            background-image: url('images/bg-amage-2.png');
        }
        .green {
            background-image: url('images/bg-amage-3.png');
        }
        .next-draw {
            text-align: center;
            margin: 10px 0;
            color: #666;
            white-space: nowrap;
            font-size: 14px;
            padding: 0 10px;
        }
        .history {
            position: absolute;
            top: 8px;
            right: 10px;
            font-size: 14px;
            color: #4dd07d;
            cursor: pointer;
            text-decoration: none;
        }
        .history:hover {
            text-decoration: underline;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            /*padding: 20px;*/
            border-radius: 8px;
            text-align: center;
            /*width: 80%;*/
            max-width: 300px;
        }
        .modal-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
        }
        .download-btn {
            display: inline-block;
            background-color: #4dd07d;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 10px;
            font-size: 14px;
        }
        .download-btn:hover {
            opacity: 0.9;
        }
        .close-modal {
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="lottery-container">
        <div class="tabs">
            <button class="tab active">新香港</button>
            <button class="tab">澳门</button>
            <button class="tab">香港</button>
        </div>
        <div class="lottery-box">
            <div class="lottery-content">
                <div id="current-period-box" style="position:absolute;top:8px;left:10px;font-size:14px;color:#333;">第<span id="current-period"></span>期</div>
                <div class="countdown">距下期开奖：<span id="countdown-timer">00:00:00</span></div>
                <a href="javascript:void(0)" class="history" id="history-link">历史记录</a>
                <div class="lottery-numbers">
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                    <div class="plus-sign">+</div>
                    <div class="number-container">
                        <div class="number"></div>
                        <div class="number-info"></div>
                    </div>
                </div>
                <div class="next-draw" id="next-draw-time"></div>
            </div>
        </div>
    </div>

    <!-- 添加弹窗 -->
    <div class="modal" id="download-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-title">查看完整历史记录</div>
            <p>请下载APP查看完整历史记录</p>
            <a href="https://www.49appdown.xyz/" class="download-btn" target="_blank">立即下载</a>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        class LotteryPage {
            constructor() {
                this.currentTabIndex = 0;
                this.tabs = document.querySelectorAll('.tab');
                this.numberContainers = document.querySelectorAll('.number-container');
                this.currentPeriodElement = document.getElementById('current-period');
                this.countdownTimer = document.getElementById('countdown-timer');
                this.nextDrawTime = document.getElementById('next-draw-time');

                // 检查元素是否正确获取
                console.log('构造函数中的nextDrawTime元素：', this.nextDrawTime); // 添加元素初始化检查

                this.updateInterval = null;
                this.countdownInterval = null;
                this.nextDrawTimeStr = '';

                this.initializeEventListeners();
                this.loadLotteryData();
                this.startCountdown();
                this.initializeModal();
            }

            initializeEventListeners() {
                document.querySelector('.tabs').addEventListener('click', event => {
                    const tab = event.target.closest('.tab');
                    if (tab && !tab.classList.contains('active')) {
                        const index = Array.from(this.tabs).indexOf(tab);
                        this.switchTab(index);
                    }
                });
            }

            initializeModal() {
                const modal = document.getElementById('download-modal');
                const historyLink = document.getElementById('history-link');
                const closeBtn = modal.querySelector('.close-modal');

                historyLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (this.currentTabIndex === 0) {
                        // 新香港在新窗口打开历史页面
                        window.open('history.html', '_blank', 'noopener,noreferrer');
                    } else {
                        // 澳门和香港显示下载弹窗
                        modal.style.display = 'block';
                    }
                });

                closeBtn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });

                window.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            }

            startCountdown() {
                if (this.countdownInterval) {
                    clearInterval(this.countdownInterval);
                }
                
                const updateCountdown = () => {
                    const now = new Date();
                    let nextDraw = new Date();
                    
                    // 根据不同彩种计算下一次开奖时间
                    if (this.currentTabIndex === 0) { // 新香港 21:35
                        nextDraw.setHours(21, 35, 0, 0);
                        if (now.getHours() >= 21 && now.getMinutes() >= 35) {
                            nextDraw.setDate(nextDraw.getDate() + 1);
                        }
                    } else if (this.currentTabIndex === 1) { // 澳门 21:30
                        nextDraw.setHours(21, 30, 0, 0);
                        if (now.getHours() >= 21 && now.getMinutes() >= 30) {
                            nextDraw.setDate(nextDraw.getDate() + 1);
                        }
                    } else { // 香港 二四六 21:30
                        const day = now.getDay(); // 0是周日，1-6是周一到周六
                        const nextDrawDays = [2, 4, 6]; // 周二、四、六
                        nextDraw.setHours(21, 30, 0, 0);
                        
                        // 找到下一个开奖日
                        let daysToAdd = 0;
                        let found = false;
                        
                        for (let i = 0; i < 7; i++) {
                            const checkDay = (day + i) % 7;
                            if (nextDrawDays.includes(checkDay)) {
                                if (i === 0 && (now.getHours() > 21 || (now.getHours() === 21 && now.getMinutes() >= 30))) {
                                    continue;
                                }
                                daysToAdd = i;
                                found = true;
                                break;
                            }
                        }
                        
                        if (!found) {
                            daysToAdd = 7 - day + 2; // 距离下周二的天数
                        }
                        
                        nextDraw.setDate(nextDraw.getDate() + daysToAdd);
                    }

                    let diff = nextDraw - now;
                    
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                    
                    this.countdownTimer.textContent = 
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                };

                // 立即执行一次
                updateCountdown();
                // 然后每秒更新
                this.countdownInterval = setInterval(updateCountdown, 1000);
            }

            switchTab(index) {
                if (this.currentTabIndex === index) return;
                
                this.currentTabIndex = index;
                this.tabs.forEach((tab, i) => {
                    if (i === index) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                });

                this.loadLotteryData();
            }

            async loadLotteryData() {
                try {
                    if (this.updateInterval) {
                        clearInterval(this.updateInterval);
                        this.updateInterval = null;
                    }

                    await this.updateLotteryData();

                    if (this.isDrawTime(this.currentTabIndex)) {
                        this.updateInterval = setInterval(() => this.updateLotteryData(), 10000);
                    }
                } catch (error) {
                    console.error('加载数据失败:', error);
                }
            }

            async updateLotteryData() {
                try {
                    const data = await api.getLotteryResults(this.currentTabIndex);
                    if (data) {
                        // 渲染数据
                        this.renderLotteryData(data);
                        // 启动倒计时
                        this.startCountdown();
                    }
                } catch (error) {
                    console.error('更新数据失败:', error);
                    this.countdownTimer.textContent = '00:00:00';
                }
            }

            renderLotteryData(data) {
                if (!data) return;
                
                console.log('接收到的数据：', data);
                
                // 更新期号
                this.currentPeriodElement.textContent = data.currentPeriod || '';

                // 更新号码和生肖五行
                const numList = data.balls || data.numbers;
                if (numList && Array.isArray(numList)) {
                    numList.forEach((num, index) => {
                        const container = this.numberContainers[index];
                        if (container && num) {
                            // 更新号码球
                            const numberElement = container.querySelector('.number');
                            if (numberElement) {
                                numberElement.className = `number ${num.color || ''}`;
                                numberElement.textContent = num.value || num.number || '';
                            }
                            // 更新生肖和五行
                            const infoElement = container.querySelector('.number-info');
                            if (infoElement) {
                                infoElement.textContent = num.zodiac || '';
                                console.log('设置生肖五行:', num.zodiac); // 添加调试日志
                            }
                        }
                    });
                }

                // 更新下期开奖时间
                if (this.currentTabIndex === 0) { // 新香港
                    if (data.nextPeriod && data.nextDrawTime) {
                        const nextDate = new Date(data.nextDrawTime);
                        const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                        const weekDay = weekDays[nextDate.getDay()];
                        // 修改时间显示为21:35
                        const dateStr = data.nextDrawTime.split(' ')[0];
                        const nextInfo = `第${data.nextPeriod}期 ${dateStr} 21:35:00 ${weekDay}`;
                        this.nextDrawTime.textContent = nextInfo;
                    } else {
                        this.nextDrawTime.textContent = '';
                    }
                } else if (this.currentTabIndex === 1) { // 澳门
                    if (data.nextPeriod && data.nextDrawTime) {
                        const period = `第2025${data.nextPeriod}期`;
                        let dateStr = data.nextDrawTime;
                        if (dateStr.length > 10) dateStr = dateStr.slice(0, 10);
                        const timeStr = '21:30:00';
                        const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                        const weekDay = weekDays[new Date(dateStr).getDay()];
                        this.nextDrawTime.innerHTML = `${period} ${dateStr} ${timeStr} ${weekDay}`;
                    } else {
                        this.nextDrawTime.innerHTML = '';
                    }
                } else if (this.currentTabIndex === 2) { // 香港
                    if (data.nextPeriod && data.nextDrawTime) {
                        const period = `第20250${data.nextPeriod}期`;
                        let dateStr = data.nextDrawTime;
                        if (dateStr.length > 10) dateStr = dateStr.slice(0, 10);
                        const timeStr = '21:32:00';
                        const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                        const weekDay = weekDays[new Date(dateStr).getDay()];
                        this.nextDrawTime.innerHTML = `${period} ${dateStr} ${timeStr} ${weekDay}`;
                    } else {
                        this.nextDrawTime.innerHTML = '';
                    }
                }

                // 检查元素是否存在
                console.log('nextDrawTime元素：', this.nextDrawTime); // 添加元素检查日志
                console.log('nextDrawTime内容：', this.nextDrawTime.textContent); // 添加内容检查日志
            }

            isDrawTime(tabIndex) {
                const now = new Date();
                const hours = now.getHours();
                const minutes = now.getMinutes();
                const day = now.getDay(); // 0是周日，1-6是周一到周六
                const time = hours * 60 + minutes;

                switch (tabIndex) {
                    case 0: // 新香港 21:33-21:43
                        return time >= 1293 && time <= 1303;
                    case 1: // 澳门 21:30-21:40
                        return time >= 1290 && time <= 1300;
                    case 2: // 香港 周二、四、六 21:30-21:45
                        if (day === 2 || day === 4 || day === 6) {
                            return time >= 1290 && time <= 1305;
                        }
                        return false;
                    default:
                        return false;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.lotteryPage = new LotteryPage();
        });
    </script>
</body>
</html> 